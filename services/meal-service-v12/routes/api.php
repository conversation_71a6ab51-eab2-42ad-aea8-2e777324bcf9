<?php

use App\Http\Controllers\Api\MealController;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Route;

/*
|--------------------------------------------------------------------------
| API Routes
|--------------------------------------------------------------------------
|
| Here is where you can register API routes for your application. These
| routes are loaded by the RouteServiceProvider and all of them will
| be assigned to the "api" middleware group. Make something great!
|
*/

// API v1 routes (for backward compatibility)
Route::prefix('v1')->group(function () {
    Route::apiResource('meals', MealController::class);
    Route::get('meals/menu/{menu}', [MealController::class, 'getByMenu']);
    Route::get('meals/type/vegetarian', [MealController::class, 'getVegetarian']);
});

// API v2 routes (for the new microservice architecture)
Route::prefix('v2')->group(function () {
    Route::apiResource('meals', MealController::class);
    Route::get('meals/menu/{menu}', [MealController::class, 'getByMenu']);
    Route::get('meals/type/vegetarian', [MealController::class, 'getVegetarian']);
});
