#!/usr/bin/env node

const fs = require('fs');
const path = require('path');

/**
 * OneFoodDialer 2025 - Build Verification Script
 * Verifies all Next.js pages are included in the production build
 */

const BUILD_DIR = '.next/server/app';
const EXPECTED_SERVICES = [
  'admin-service-v12',
  'analytics-service-v12', 
  'auth-service-v12',
  'catalogue-service-v12',
  'customer-service-v12',
  'delivery-service-v12',
  'kitchen-service-v12',
  'meal-service-v12',
  'notification-service-v12',
  'payment-service-v12',
  'quickserve-service-v12',
  'subscription-service-v12'
];

function findPageFiles(dir, pages = []) {
  if (!fs.existsSync(dir)) {
    return pages;
  }

  const files = fs.readdirSync(dir);
  
  for (const file of files) {
    const filePath = path.join(dir, file);
    const stat = fs.statSync(filePath);
    
    if (stat.isDirectory()) {
      findPageFiles(filePath, pages);
    } else if (file === 'page.js') {
      // Convert file path to route
      const route = filePath
        .replace(BUILD_DIR, '')
        .replace('/page.js', '')
        .replace(/\\/g, '/')
        .replace(/\/\(microfrontend-v2\)/, '');
      
      pages.push(route || '/');
    }
  }
  
  return pages;
}

function categorizePages(pages) {
  const categories = {
    core: [],
    microservices: {},
    admin: [],
    customer: [],
    public: [],
    auth: [],
    dashboard: [],
    other: []
  };

  for (const page of pages) {
    if (page === '/') {
      categories.core.push(page);
    } else if (page.startsWith('/admin/')) {
      categories.admin.push(page);
    } else if (page.startsWith('/customer/')) {
      categories.customer.push(page);
    } else if (page.startsWith('/public/')) {
      categories.public.push(page);
    } else if (page.includes('auth')) {
      categories.auth.push(page);
    } else if (page.startsWith('/dashboard')) {
      categories.dashboard.push(page);
    } else {
      // Check if it's a microservice
      let categorized = false;
      for (const service of EXPECTED_SERVICES) {
        if (page.startsWith(`/${service}`)) {
          if (!categories.microservices[service]) {
            categories.microservices[service] = [];
          }
          categories.microservices[service].push(page);
          categorized = true;
          break;
        }
      }
      
      if (!categorized) {
        categories.other.push(page);
      }
    }
  }

  return categories;
}

function generateReport(categories) {
  console.log('\n🎯 ONEFOODDIALER 2025 - BUILD VERIFICATION REPORT');
  console.log('=' .repeat(60));
  
  let totalPages = 0;
  
  // Core Pages
  console.log('\n📱 CORE APPLICATION PAGES');
  console.log('-'.repeat(30));
  categories.core.forEach(page => console.log(`  ✅ ${page}`));
  categories.dashboard.forEach(page => console.log(`  ✅ ${page}`));
  categories.auth.forEach(page => console.log(`  ✅ ${page}`));
  totalPages += categories.core.length + categories.dashboard.length + categories.auth.length;
  
  // Microservices
  console.log('\n🔧 MICROSERVICE PAGES');
  console.log('-'.repeat(30));
  
  for (const service of EXPECTED_SERVICES) {
    const servicePages = categories.microservices[service] || [];
    console.log(`\n  📦 ${service.toUpperCase()} (${servicePages.length} pages)`);
    
    if (servicePages.length === 0) {
      console.log('    ❌ No pages found!');
    } else {
      // Show first 5 pages as examples
      servicePages.slice(0, 5).forEach(page => {
        console.log(`    ✅ ${page}`);
      });
      
      if (servicePages.length > 5) {
        console.log(`    ... and ${servicePages.length - 5} more pages`);
      }
    }
    
    totalPages += servicePages.length;
  }
  
  // Admin Portal
  console.log('\n⚙️ ADMIN PORTAL PAGES');
  console.log('-'.repeat(30));
  categories.admin.forEach(page => console.log(`  ✅ ${page}`));
  totalPages += categories.admin.length;
  
  // Customer Portal  
  console.log('\n👥 CUSTOMER PORTAL PAGES');
  console.log('-'.repeat(30));
  categories.customer.forEach(page => console.log(`  ✅ ${page}`));
  totalPages += categories.customer.length;
  
  // Public Pages
  console.log('\n🌐 PUBLIC PAGES');
  console.log('-'.repeat(30));
  categories.public.forEach(page => console.log(`  ✅ ${page}`));
  totalPages += categories.public.length;
  
  // Other Pages
  if (categories.other.length > 0) {
    console.log('\n📄 OTHER PAGES');
    console.log('-'.repeat(30));
    categories.other.forEach(page => console.log(`  ✅ ${page}`));
    totalPages += categories.other.length;
  }
  
  // Summary
  console.log('\n📊 SUMMARY STATISTICS');
  console.log('=' .repeat(60));
  console.log(`🎯 Total Pages Built: ${totalPages}`);
  console.log(`🔧 Microservices: ${EXPECTED_SERVICES.length}`);
  console.log(`📱 Core Pages: ${categories.core.length + categories.dashboard.length + categories.auth.length}`);
  console.log(`⚙️ Admin Pages: ${categories.admin.length}`);
  console.log(`👥 Customer Pages: ${categories.customer.length}`);
  console.log(`🌐 Public Pages: ${categories.public.length}`);
  
  // Service breakdown
  console.log('\n🔧 MICROSERVICE BREAKDOWN:');
  for (const service of EXPECTED_SERVICES) {
    const count = categories.microservices[service]?.length || 0;
    const status = count > 0 ? '✅' : '❌';
    console.log(`  ${status} ${service}: ${count} pages`);
  }
  
  console.log('\n✅ BUILD VERIFICATION COMPLETE!');
  console.log(`🎉 All ${totalPages} pages successfully built and included in production bundle.`);
  
  return totalPages;
}

// Main execution
try {
  console.log('🔍 Scanning build directory for pages...');
  const pages = findPageFiles(BUILD_DIR);
  const categories = categorizePages(pages);
  const totalPages = generateReport(categories);
  
  // Verify expected minimum
  const EXPECTED_MINIMUM = 500;
  if (totalPages >= EXPECTED_MINIMUM) {
    console.log(`\n🎯 SUCCESS: Found ${totalPages} pages (expected minimum: ${EXPECTED_MINIMUM})`);
    process.exit(0);
  } else {
    console.log(`\n❌ WARNING: Only found ${totalPages} pages (expected minimum: ${EXPECTED_MINIMUM})`);
    process.exit(1);
  }
  
} catch (error) {
  console.error('❌ Error during verification:', error.message);
  process.exit(1);
}
